# Precision Movement Feature Documentation

## Overview

The Precision Movement feature enhances the lasso tool functionality by allowing users to move selected shapes with pixel-level precision. This is particularly useful for correcting alignment issues when base imagery is offset by a few pixels relative to the shapes.

## Features

### 1. Intuitive User Interface
- **Tabbed Interface**: The lasso control dialog now includes a "Precision Move" tab alongside the existing "Items" tab
- **Arrow Key Controls**: Visual arrow buttons for directional movement
- **Input Fields**: Precise X/Y offset input with pixel values
- **Movement Controls**: Toggle precision mode, undo moves, and preview functionality

### 2. Movement Methods

#### Arrow Key Controls
- **Visual Buttons**: Click and hold arrow buttons for continuous movement
- **Keyboard Support**: Use actual arrow keys when precision mode is active
- **Configurable Step Size**: Adjust movement increment (1-50 pixels)
- **Hold to Repeat**: Optional continuous movement while holding buttons/keys

#### Precise Input
- **X/Y Offset Fields**: Enter exact pixel offsets for precise positioning
- **Apply Offset Button**: Execute the specified movement
- **Enter Key Support**: Press Enter in input fields to apply movement

### 3. Visual Feedback
- **Movement Status**: Real-time display of total movement (X/Y pixels)
- **Move History**: Track number of moves for undo functionality
- **Preview Mode**: Show preview overlays of shape positions before applying
- **Active Mode Indicator**: Clear visual indication when precision mode is active

### 4. Undo/Redo Support
- **Move History**: Each movement is tracked for undo capability
- **Undo Last Move**: Reverse the most recent movement operation
- **Position Restoration**: Return shapes to their original positions if needed

## How to Use

### Step 1: Select Shapes with Lasso Tool
1. Click the Lasso tool in the toolbar
2. Draw a lasso around the shapes you want to move
3. The lasso control dialog will open showing selected items

### Step 2: Enter Precision Move Mode
1. Click the "Precision Move" button in the dialog
2. The main dialog disappears and a floating control panel appears
3. The floating panel shows movement controls and can be dragged anywhere

### Step 3: Move Shapes
Choose one of the movement methods:

#### Using Arrow Controls
- Click arrow buttons for single-step movement
- Hold buttons for continuous movement (if "Hold to repeat" is enabled)
- Adjust step size using the "Step (pixels)" field

#### Using Keyboard
- Use arrow keys on your keyboard for movement
- Press Escape to exit precision mode
- Movement respects the current step size setting

#### Using Precise Input
- Enter X and Y offset values in pixels
- Click "Apply Offset" or press Enter to execute movement
- Positive X moves right, negative X moves left
- Positive Y moves down, negative Y moves up

### Step 4: Review and Finalize
- Use "Undo Move" to reverse the last movement
- Monitor total movement in the status display
- **Apply Changes**: Click "Apply" to confirm and save the precision movements
- **Cancel Changes**: Click "Cancel" or "X" to revert all movements and return to original positions
- The main dialog reappears for other lasso actions (delete, change layer, etc.)

## Technical Implementation

### Architecture
- **Component**: Enhanced `lassocontrol.js` with precision movement functionality
- **Integration**: Seamless integration with existing lasso tool workflow
- **Compatibility**: Maintains all existing lasso tool features

### Key Methods
- `togglePrecisionMove()`: Enter/exit precision movement mode
- `moveShapes(deltaX, deltaY)`: Core movement logic with pixel-to-coordinate conversion
- `undoLastMove()`: Reverse the most recent movement
- `pixelsToLatLng()`: Convert pixel offsets to map coordinate offsets

### Data Structures
- `moveHistory[]`: Array of movement records for undo functionality
- `originalPositions`: Map storing initial positions of selected shapes
- `totalMoveX/Y`: Cumulative movement tracking

## Supported Shape Types
- **Markers**: Point features with icon representation
- **Polygons**: Closed shape features
- **Polylines**: Line features
- **Circles**: Circular features
- **Multi-geometry**: Complex shapes with multiple parts

## Use Cases

### Primary Use Case: Imagery Offset Correction
When base imagery layers are offset by small amounts (typically 1-10 pixels), users can:
1. Select affected shapes with the lasso tool
2. Use precision movement to align shapes with the correct imagery position
3. Apply fine-tuned adjustments for perfect alignment

### Additional Use Cases
- **Layout Adjustments**: Fine-tune shape positioning for better visual layout
- **Alignment Corrections**: Align shapes with grid lines or other reference points
- **Batch Positioning**: Move multiple related shapes together while maintaining their relative positions

## Keyboard Shortcuts
- **Arrow Keys**: Move shapes in precision mode
- **Escape**: Cancel precision movement mode (reverts all changes)

## Action Buttons
- **Apply**: Confirms and saves all precision movements made during the session
- **Cancel**: Reverts all movements back to original positions and exits precision mode
- **Undo Move**: Reverses only the last movement operation (can be used multiple times)

## Best Practices
1. **Start Small**: Begin with small step sizes (1-2 pixels) for fine adjustments
2. **Use Preview**: Enable preview mode to see changes before applying
3. **Track Movement**: Monitor the total movement display to understand cumulative changes
4. **Save Frequently**: Save your map after making precision adjustments
5. **Test Alignment**: Zoom in to verify precise alignment after movement

## Troubleshooting

### Common Issues
- **Shapes Not Moving**: Ensure precision mode is active and shapes are properly selected
- **Keyboard Not Working**: Check that precision mode is active and focus is on the map
- **Unexpected Movement**: Verify step size settings and coordinate system orientation

### Performance Considerations
- **Large Selections**: Moving many shapes simultaneously may impact performance
- **Complex Shapes**: Shapes with many vertices may take longer to process
- **Preview Mode**: Disable preview for better performance with large selections

## Future Enhancements
- **Grid Snapping**: Snap movements to grid increments
- **Relative Movement**: Move shapes relative to a reference point
- **Batch Operations**: Apply the same movement to multiple lasso selections
- **Movement Templates**: Save and reuse common movement patterns

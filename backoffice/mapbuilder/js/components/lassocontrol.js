import UtilityLib from "../components/utility/utilitylib.js";

export default {
    template: /*HTML*/`<div>
    <v-dialog overlay-opacity="0" max-width="50vw" v-model="lasooDialog">
        <v-card>
            <v-toolbar class="elevation-0 white">
                <v-toolbar-title>
                    <h3 class="headline mb-0">Selected Items</h3>
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon @click="lasooDialog=false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text>
                <v-tabs v-model="activeTab" background-color="transparent" color="primary">
                    <v-tab>Items</v-tab>
                    <v-tab>Precision Move</v-tab>
                </v-tabs>

                <v-tabs-items v-model="activeTab">
                    <!-- Items Tab -->
                    <v-tab-item>
                        <v-simple-table dense fixed-header height="35vh" class="mt-3">
                            <template v-slot:default>
                                <thead>
                                <tr>
                                    <th class="text-left">Name</th>
                                    <th class="text-left">Layer</th>
                                    <th class="text-left">Type</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="item in myData.layers" :key="item.id">
                                    <td>{{item.layerName}}</td>
                                    <td>{{ item.groupName }}</td>
                                    <td>{{ item.groupType }}</td>
                                </tr>
                                </tbody>
                            </template>
                        </v-simple-table>
                    </v-tab-item>

                    <!-- Precision Move Tab -->
                    <v-tab-item>
                        <div class="mt-3">
                            <v-row>
                                <v-col cols="12">
                                    <v-alert type="info" dense outlined class="mb-3">
                                        <small>Use arrow keys or input fields to move selected shapes with pixel precision. Press Escape to cancel movement mode.</small>
                                    </v-alert>
                                </v-col>
                            </v-row>

                            <v-row>
                                <v-col cols="6">
                                    <v-card outlined class="pa-3">
                                        <v-card-title class="text-subtitle-1 pa-0 mb-2">Arrow Key Controls</v-card-title>
                                        <div class="text-center">
                                            <v-btn
                                                icon
                                                large
                                                @mousedown="startContinuousMove('up')"
                                                @mouseup="stopContinuousMove"
                                                @mouseleave="stopContinuousMove"
                                                :disabled="!precisionMoveActive"
                                                class="mb-2"
                                            >
                                                <v-icon>keyboard_arrow_up</v-icon>
                                            </v-btn>
                                            <br>
                                            <v-btn
                                                icon
                                                large
                                                @mousedown="startContinuousMove('left')"
                                                @mouseup="stopContinuousMove"
                                                @mouseleave="stopContinuousMove"
                                                :disabled="!precisionMoveActive"
                                                class="mr-2"
                                            >
                                                <v-icon>keyboard_arrow_left</v-icon>
                                            </v-btn>
                                            <v-btn
                                                icon
                                                large
                                                @mousedown="startContinuousMove('right')"
                                                @mouseup="stopContinuousMove"
                                                @mouseleave="stopContinuousMove"
                                                :disabled="!precisionMoveActive"
                                                class="ml-2"
                                            >
                                                <v-icon>keyboard_arrow_right</v-icon>
                                            </v-btn>
                                            <br>
                                            <v-btn
                                                icon
                                                large
                                                @mousedown="startContinuousMove('down')"
                                                @mouseup="stopContinuousMove"
                                                @mouseleave="stopContinuousMove"
                                                :disabled="!precisionMoveActive"
                                                class="mt-2"
                                            >
                                                <v-icon>keyboard_arrow_down</v-icon>
                                            </v-btn>
                                        </div>
                                        <v-divider class="my-3"></v-divider>
                                        <v-row dense>
                                            <v-col cols="6">
                                                <v-text-field
                                                    v-model.number="moveStep"
                                                    label="Step (pixels)"
                                                    type="number"
                                                    min="1"
                                                    max="50"
                                                    dense
                                                    outlined
                                                ></v-text-field>
                                            </v-col>
                                            <v-col cols="6" class="d-flex align-center">
                                                <v-checkbox
                                                    v-model="holdToRepeat"
                                                    label="Hold to repeat"
                                                    dense
                                                ></v-checkbox>
                                            </v-col>
                                        </v-row>
                                    </v-card>
                                </v-col>

                                <v-col cols="6">
                                    <v-card outlined class="pa-3">
                                        <v-card-title class="text-subtitle-1 pa-0 mb-2">Precise Input</v-card-title>
                                        <v-row dense>
                                            <v-col cols="6">
                                                <v-text-field
                                                    v-model.number="offsetX"
                                                    label="X Offset (pixels)"
                                                    type="number"
                                                    dense
                                                    outlined
                                                    @keyup.enter="applyOffset"
                                                ></v-text-field>
                                            </v-col>
                                            <v-col cols="6">
                                                <v-text-field
                                                    v-model.number="offsetY"
                                                    label="Y Offset (pixels)"
                                                    type="number"
                                                    dense
                                                    outlined
                                                    @keyup.enter="applyOffset"
                                                ></v-text-field>
                                            </v-col>
                                        </v-row>
                                        <v-btn
                                            block
                                            color="primary"
                                            @click="applyOffset"
                                            :disabled="!precisionMoveActive || (!offsetX && !offsetY)"
                                            class="mb-2"
                                        >
                                            Apply Offset
                                        </v-btn>
                                        <v-btn
                                            block
                                            outlined
                                            @click="resetOffset"
                                            :disabled="!precisionMoveActive"
                                        >
                                            Reset Position
                                        </v-btn>
                                    </v-card>
                                </v-col>
                            </v-row>

                            <v-row>
                                <v-col cols="12">
                                    <v-card outlined class="pa-3">
                                        <v-card-title class="text-subtitle-1 pa-0 mb-2">Movement Controls</v-card-title>
                                        <v-row dense>
                                            <v-col cols="4">
                                                <v-btn
                                                    block
                                                    color="success"
                                                    @click="togglePrecisionMove"
                                                    :loading="precisionMoveLoading"
                                                >
                                                    {{ precisionMoveActive ? 'Exit Move Mode' : 'Enter Move Mode' }}
                                                </v-btn>
                                            </v-col>
                                            <v-col cols="4">
                                                <v-btn
                                                    block
                                                    color="warning"
                                                    @click="undoLastMove"
                                                    :disabled="!precisionMoveActive || moveHistory.length === 0"
                                                >
                                                    Undo Move
                                                </v-btn>
                                            </v-col>
                                            <v-col cols="4">
                                                <v-btn
                                                    block
                                                    color="info"
                                                    @click="previewMove"
                                                    :disabled="!precisionMoveActive"
                                                >
                                                    {{ showPreview ? 'Hide Preview' : 'Show Preview' }}
                                                </v-btn>
                                            </v-col>
                                        </v-row>
                                        <v-row dense class="mt-2" v-if="precisionMoveActive">
                                            <v-col cols="12">
                                                <v-alert type="success" dense outlined>
                                                    <small>
                                                        <strong>Precision Move Mode Active</strong><br>
                                                        Total moved: X: {{ totalMoveX }}px, Y: {{ totalMoveY }}px<br>
                                                        {{ moveHistory.length }} move(s) in history
                                                    </small>
                                                </v-alert>
                                            </v-col>
                                        </v-row>
                                    </v-card>
                                </v-col>
                            </v-row>
                        </div>
                    </v-tab-item>
                </v-tabs-items>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn text @click="lasooDialog=false">Cancel</v-btn>
                <v-menu offset-y v-if="myData.sameGroup && myData.presets.length>0">
                    <template v-slot:activator="{ on, attrs }">
                        <v-btn text v-bind="attrs"
                               v-on="on">Change Layer
                        </v-btn>
                    </template>
                    <v-list dense style="height:75vh;overflow-y:auto">
                        <v-list-item dense
                                v-for="(item, index) in myData.presets"
                                :key="index"
                                @click="changeGroup(item.value)"
                        >
                            <v-list-item-title>{{ item.text }}</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
                <v-btn text @click="deleteAllFeatures">Delete</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
    <UtilityLib ref="utilityLib"></UtilityLib>
</div>`,
    watch: {},
    props: [],
    components: {UtilityLib},
    created() {
        this.interval = setInterval(() => {
            if (map != null) {
                clearInterval(this.interval);
                L.DomEvent.addListener(map, 'pm:lasoostart', this.startLasso, this);
            }
        }, 500);


    },
    mounted() {

    },

    data: function () {
        return {
            lasooDialog: false,
            activeTab: 0,
            myData: {
                layers: [],
                sameGroup: false,
                groupName: "",
                presets: [],
                groupType: "",
            },
            // Precision movement data
            precisionMoveActive: false,
            precisionMoveLoading: false,
            moveStep: 1,
            holdToRepeat: false,
            offsetX: 0,
            offsetY: 0,
            totalMoveX: 0,
            totalMoveY: 0,
            showPreview: false,
            moveHistory: [],
            originalPositions: new Map(),
            previewLayers: [],
            continuousMoveInterval: null,
            keyboardListenerActive: false,
        };
    },
    methods: {
        startLasso(e) {
            L.DomEvent.stopPropagation(e);

            this.lassoActive = true;
            globalVueMapInstance.stopEditing();
            globalVueMapInstance.removeItemPopup();


            //this.lasso = map.selectAreaFeature.enable();
            L.DomEvent.addListener(map, 'pm:lasoocreate', this.lassoChange, this);
        },
        getSelectedFeatures(e) {
            const temp = e.layer;
            const layersFound = [];
          
            drawnGroups.eachLayer(player => {
              player.eachLayer(vlayer => {
                try {
                  if (turf.booleanContains(temp.toGeoJSON(15), vlayer.toGeoJSON(15))) {
                    layersFound.push(vlayer);
                  }
                } catch (error) {
                  console.log(error);
                }
              });
            });
          
            return layersFound.length === 0 ? null : layersFound;
          },
          processSelectedLayers() {
            let sameGroup = true;
            const k = [...new Set(this.lassoFeatures)];
            this.myData.layers = [];
          
            k.forEach((item, i) => {
              if (item.options?.type === "lassoselect") {
                k.splice(i, 1);
              }
            });
          
            const group = drawnGroups.searchLayerParent(k[0]._leaflet_id).metadata.shortname;
            const grouptype = this.groupType(drawnGroups.searchLayerParent(k[0]._leaflet_id));
          
            for (const item of k) {
              const temp = drawnGroups.searchLayerParent(item._leaflet_id);
              const thisgrouptype = this.groupType(temp);
          
              if (thisgrouptype !== grouptype) {
                sameGroup = false;
              }
          
              const layer = {
                id: item._leaflet_id,
                groupType: thisgrouptype,
                preset: temp.metadata.shortname,
                groupName: globalMapManager.getPreset(temp.metadata.shortname).name,
                layerName:
                  thisgrouptype !== "Photo"
                    ? item.feature.properties.name
                    : `Photo${item.feature.properties.number}`,
              };
          
              this.myData.layers.push(layer);
            }
          
            this.myData.groupType = grouptype;
            this.myData.sameGroup = sameGroup;
            this.myData.groupName = group;
            this.myData.presets = [];
          
            if (sameGroup) {
              const presets = globalMapManager.getPresets();
          
              if (globalMapManager.getPreset(group).photogroup) {
                for (const property in presets) {
                  if (
                    presets[property].photogroup &&
                    property !== "photos" &&
                    Object.keys(presets[property].leafletlayer._layers).length > 0
                  ) {
                    this.myData.presets.push({
                      value: presets[property].shortname,
                      text: presets[property].name,
                    });
                  }
                }
              } else if (globalMapManager.getPreset(group).markergroup) {
                for (const property in presets) {
                  if (
                    presets[property].markergroup &&
                    property !== "markers" &&
                    Object.keys(presets[property].leafletlayer._layers).length > 0
                  ) {
                    this.myData.presets.push({
                      value: presets[property].shortname,
                      text: presets[property].name,
                    });
                  }
                }
              } else if (globalMapManager.getPreset(group).vectorgroup) {
                for (const property in presets) {
                  if (
                    presets[property].vectorgroup &&
                    property !== "none" &&
                    property !== "hole"
                  ) {
                    this.myData.presets.push({
                      value: presets[property].shortname,
                      text: presets[property].name,
                    });
                  }
                }
              }
            }
          },
       
        groupType(group){
            if (group.metadata.textgroup)
                return "Text";
            if (group.metadata.vectorgroup)
                return "Vector";
            if (group.metadata.photogroup)
                return "Photo";
            if (group.metadata.markergroup)
                return "Marker";
        },
        lassoChange(e) {
            this.lassoFeatures = this.getSelectedFeatures(e);

            map.pm.Draw.disable();

            map.removeLayer(e.layer)
            if (this.lassoFeatures != null) {
                this.processSelectedLayers();
                this.lasooDialog = true;
            }
        },
        changeGroup(e) {
            var grp = e;
            for (var i = 0; i < this.myData.layers.length; i++) {
                var groupID = drawnGroups.searchLayerParent(this.myData.layers[i].id)
                    .metadata
                    .shortname;
                let layer = drawnGroups.searchLayer(this.myData.layers[i].id)
              
                globalMapManager.removeLayerPreset(groupID, layer)
                globalMapManager.addLayerPreset(grp, layer)

                layer.setStyle(
                    globalMapManager.getPreset(grp).leafletlayer.metadata
                );

                layer.feature.properties.preset = grp;
            }

            this.lasooDialog = false;

        },
        deleteAllFeatures() {
            this.$refs.utilityLib.open("Confirm", "Are you sure you want to delete all selected features?", "Yes", "No", true).then(function (result) {
                if (result) {


                    for (var i = 0; i < this.myData.layers.length; i++) {
                        var groupID = drawnGroups.searchLayerParent(this.myData.layers[i].id)
                            .metadata
                            .shortname;

                        globalMapManager.removeLayerPreset(groupID, this.myData.layers[i].id)


                    }

                    this.lasooDialog = false;

                }
            }.bind(this))
        },

        // Precision Movement Methods
        togglePrecisionMove() {
            this.precisionMoveLoading = true;

            if (this.precisionMoveActive) {
                this.exitPrecisionMove();
            } else {
                this.enterPrecisionMove();
            }

            this.precisionMoveLoading = false;
        },

        enterPrecisionMove() {
            this.precisionMoveActive = true;
            this.totalMoveX = 0;
            this.totalMoveY = 0;
            this.moveHistory = [];
            this.originalPositions.clear();

            // Store original positions for all selected layers
            this.lassoFeatures.forEach(layer => {
                if (layer instanceof L.Marker) {
                    this.originalPositions.set(layer._leaflet_id, {
                        latlng: layer.getLatLng(),
                        type: 'marker'
                    });
                } else if (layer.getLatLngs) {
                    this.originalPositions.set(layer._leaflet_id, {
                        latlngs: JSON.parse(JSON.stringify(layer.getLatLngs())),
                        type: 'shape'
                    });
                }
            });

            this.setupKeyboardListeners();
        },

        exitPrecisionMove() {
            this.precisionMoveActive = false;
            this.clearPreviewLayers();
            this.removeKeyboardListeners();
            this.stopContinuousMove();
            this.showPreview = false;
        },

        setupKeyboardListeners() {
            if (this.keyboardListenerActive) return;

            this.keyboardListenerActive = true;
            document.addEventListener('keydown', this.handleKeyDown);
            document.addEventListener('keyup', this.handleKeyUp);
        },

        removeKeyboardListeners() {
            if (!this.keyboardListenerActive) return;

            this.keyboardListenerActive = false;
            document.removeEventListener('keydown', this.handleKeyDown);
            document.removeEventListener('keyup', this.handleKeyUp);
        },

        handleKeyDown(event) {
            if (!this.precisionMoveActive) return;

            // Prevent default behavior for arrow keys
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Escape'].includes(event.key)) {
                event.preventDefault();
            }

            switch (event.key) {
                case 'ArrowUp':
                    this.moveShapes(0, -this.moveStep);
                    break;
                case 'ArrowDown':
                    this.moveShapes(0, this.moveStep);
                    break;
                case 'ArrowLeft':
                    this.moveShapes(-this.moveStep, 0);
                    break;
                case 'ArrowRight':
                    this.moveShapes(this.moveStep, 0);
                    break;
                case 'Escape':
                    this.exitPrecisionMove();
                    break;
            }
        },

        handleKeyUp(event) {
            // Handle key up events if needed for continuous movement
        },

        startContinuousMove(direction) {
            if (!this.holdToRepeat) {
                this.moveSingleStep(direction);
                return;
            }

            this.stopContinuousMove();
            this.moveSingleStep(direction);

            this.continuousMoveInterval = setInterval(() => {
                this.moveSingleStep(direction);
            }, 100);
        },

        stopContinuousMove() {
            if (this.continuousMoveInterval) {
                clearInterval(this.continuousMoveInterval);
                this.continuousMoveInterval = null;
            }
        },

        moveSingleStep(direction) {
            const step = this.moveStep;
            switch (direction) {
                case 'up':
                    this.moveShapes(0, -step);
                    break;
                case 'down':
                    this.moveShapes(0, step);
                    break;
                case 'left':
                    this.moveShapes(-step, 0);
                    break;
                case 'right':
                    this.moveShapes(step, 0);
                    break;
            }
        },

        applyOffset() {
            if (!this.offsetX && !this.offsetY) return;

            this.moveShapes(this.offsetX || 0, this.offsetY || 0);
            this.offsetX = 0;
            this.offsetY = 0;
        },

        resetOffset() {
            this.offsetX = 0;
            this.offsetY = 0;
        },

        moveShapes(deltaX, deltaY) {
            if (!this.precisionMoveActive || (!deltaX && !deltaY)) return;

            // Store move in history for undo
            const moveRecord = {
                deltaX,
                deltaY,
                timestamp: Date.now()
            };
            this.moveHistory.push(moveRecord);

            // Update total movement
            this.totalMoveX += deltaX;
            this.totalMoveY += deltaY;

            // Convert pixel offset to lat/lng offset
            const pixelOffset = this.pixelsToLatLng(deltaX, deltaY);

            // Move each selected layer
            this.lassoFeatures.forEach(layer => {
                this.moveLayer(layer, pixelOffset.lat, pixelOffset.lng);
            });

            // Update layer properties and trigger save
            this.lassoFeatures.forEach(layer => {
                globalVueMapInstance.updateLayerProps(layer);
                const groupId = drawnGroups.searchLayerParent(layer._leaflet_id);
                if (groupId && groupId.metadata) {
                    globalMapManager.updateLayerInPanel(groupId.metadata.shortname, layer);
                }
            });
        },

        moveLayer(layer, deltaLat, deltaLng) {
            if (layer instanceof L.Marker) {
                const currentPos = layer.getLatLng();
                layer.setLatLng([currentPos.lat + deltaLat, currentPos.lng + deltaLng]);
            } else if (layer.getLatLngs) {
                const currentLatLngs = layer.getLatLngs();
                const newLatLngs = this.offsetLatLngs(currentLatLngs, deltaLat, deltaLng);
                layer.setLatLngs(newLatLngs);
            }
        },

        offsetLatLngs(latlngs, deltaLat, deltaLng) {
            if (Array.isArray(latlngs[0])) {
                // Multi-dimensional array (polygon with holes)
                return latlngs.map(ring => this.offsetLatLngs(ring, deltaLat, deltaLng));
            } else {
                // Simple array of LatLng objects
                return latlngs.map(latlng => ({
                    lat: latlng.lat + deltaLat,
                    lng: latlng.lng + deltaLng
                }));
            }
        },

        pixelsToLatLng(deltaX, deltaY) {
            // Get map bounds and container size to calculate pixel to lat/lng ratio
            const mapBounds = map.getBounds();
            const mapSize = map.getSize();

            const latPerPixel = (mapBounds.getNorth() - mapBounds.getSouth()) / mapSize.y;
            const lngPerPixel = (mapBounds.getEast() - mapBounds.getWest()) / mapSize.x;

            return {
                lat: -deltaY * latPerPixel, // Negative because screen Y increases downward
                lng: deltaX * lngPerPixel
            };
        },

        undoLastMove() {
            if (this.moveHistory.length === 0) return;

            const lastMove = this.moveHistory.pop();

            // Reverse the last move
            this.totalMoveX -= lastMove.deltaX;
            this.totalMoveY -= lastMove.deltaY;

            // Convert pixel offset to lat/lng offset (reverse direction)
            const pixelOffset = this.pixelsToLatLng(-lastMove.deltaX, -lastMove.deltaY);

            // Move each selected layer back
            this.lassoFeatures.forEach(layer => {
                this.moveLayer(layer, pixelOffset.lat, pixelOffset.lng);
            });

            // Update layer properties
            this.lassoFeatures.forEach(layer => {
                globalVueMapInstance.updateLayerProps(layer);
                const groupId = drawnGroups.searchLayerParent(layer._leaflet_id);
                if (groupId && groupId.metadata) {
                    globalMapManager.updateLayerInPanel(groupId.metadata.shortname, layer);
                }
            });
        },

        previewMove() {
            this.showPreview = !this.showPreview;

            if (this.showPreview) {
                this.createPreviewLayers();
            } else {
                this.clearPreviewLayers();
            }
        },

        createPreviewLayers() {
            this.clearPreviewLayers();

            this.lassoFeatures.forEach(layer => {
                let previewLayer;

                if (layer instanceof L.Marker) {
                    const pos = layer.getLatLng();
                    previewLayer = L.marker([pos.lat, pos.lng], {
                        icon: layer.options.icon,
                        opacity: 0.5
                    });
                } else if (layer instanceof L.Polygon) {
                    previewLayer = L.polygon(layer.getLatLngs(), {
                        ...layer.options,
                        opacity: 0.3,
                        fillOpacity: 0.1,
                        color: '#ff0000',
                        dashArray: '5,5'
                    });
                } else if (layer instanceof L.Polyline) {
                    previewLayer = L.polyline(layer.getLatLngs(), {
                        ...layer.options,
                        opacity: 0.3,
                        color: '#ff0000',
                        dashArray: '5,5'
                    });
                } else if (layer instanceof L.Circle) {
                    previewLayer = L.circle(layer.getLatLng(), {
                        ...layer.options,
                        opacity: 0.3,
                        fillOpacity: 0.1,
                        color: '#ff0000',
                        dashArray: '5,5'
                    });
                }

                if (previewLayer) {
                    previewLayer.addTo(map);
                    this.previewLayers.push(previewLayer);
                }
            });
        },

        clearPreviewLayers() {
            this.previewLayers.forEach(layer => {
                map.removeLayer(layer);
            });
            this.previewLayers = [];
        }
    },

    beforeDestroy() {
        this.removeKeyboardListeners();
        this.stopContinuousMove();
        this.clearPreviewLayers();
    }
}

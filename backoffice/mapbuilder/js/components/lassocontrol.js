import UtilityLib from "../components/utility/utilitylib.js";

export default {
    template: /*HTML*/`<div>
    <!-- Main Lasso Dialog - Simplified -->
    <v-dialog overlay-opacity="0" max-width="400px" v-model="lasooDialog" persistent>
        <v-card>
            <v-toolbar class="elevation-0 white" dense>
                <v-toolbar-title>
                    <h4 class="subtitle-1 mb-0">Selected Items ({{ myData.layers.length }})</h4>
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon small @click="closeLassoDialog">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="pb-2">
                <v-simple-table dense fixed-header height="200px" class="mb-3">
                    <template v-slot:default>
                        <thead>
                        <tr>
                            <th class="text-left">Name</th>
                            <th class="text-left">Layer</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="item in myData.layers" :key="item.id">
                            <td class="text-caption">{{item.layerName}}</td>
                            <td class="text-caption">{{ item.groupName }}</td>
                        </tr>
                        </tbody>
                    </template>
                </v-simple-table>
            </v-card-text>
            <v-card-actions class="pt-0">
                <v-btn
                    small
                    color="primary"
                    @click="startPrecisionMove"
                    :disabled="precisionMoveActive"
                >
                    <v-icon small left>open_with</v-icon>
                    Precision Move
                </v-btn>
                <v-spacer></v-spacer>
                <v-menu offset-y v-if="myData.sameGroup && myData.presets.length>0">
                    <template v-slot:activator="{ on, attrs }">
                        <v-btn small text v-bind="attrs" v-on="on">
                            Change Layer
                        </v-btn>
                    </template>
                    <v-list dense style="height:200px;overflow-y:auto">
                        <v-list-item dense
                                v-for="(item, index) in myData.presets"
                                :key="index"
                                @click="changeGroup(item.value)"
                        >
                            <v-list-item-title>{{ item.text }}</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
                <v-btn small text color="error" @click="deleteAllFeatures">Delete</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>

    <!-- Floating Precision Movement Control Panel -->
    <v-card
        v-if="precisionMoveActive"
        class="precision-move-panel elevation-8"
        :style="precisionPanelStyle"
    >
        <v-card-title
            class="pa-2 primary white--text"
            @mousedown="startDrag"
            @mousemove="drag"
            @mouseup="stopDrag"
            @mouseleave="stopDrag"
        >
            <v-icon small color="white" class="mr-2">open_with</v-icon>
            <span class="subtitle-2">Precision Move</span>
            <v-spacer></v-spacer>
            <v-btn icon x-small color="white" @click="exitPrecisionMove">
                <v-icon>close</v-icon>
            </v-btn>
        </v-card-title>

        <v-card-text class="pa-3">
            <!-- Arrow Controls - Compact Layout -->
            <div class="text-center mb-3">
                <v-btn
                    icon
                    small
                    @click="moveShapes(0, -moveStep)"
                    class="mb-1 move-btn"
                    color="primary"
                >
                    <v-icon>keyboard_arrow_up</v-icon>
                </v-btn>
                <br>
                <v-btn
                    icon
                    small
                    @click="moveShapes(-moveStep, 0)"
                    class="mr-1 move-btn"
                    color="primary"
                >
                    <v-icon>keyboard_arrow_left</v-icon>
                </v-btn>
                <v-btn
                    icon
                    small
                    @click="moveShapes(moveStep, 0)"
                    class="ml-1 move-btn"
                    color="primary"
                >
                    <v-icon>keyboard_arrow_right</v-icon>
                </v-btn>
                <br>
                <v-btn
                    icon
                    small
                    @click="moveShapes(0, moveStep)"
                    class="mt-1 move-btn"
                    color="primary"
                >
                    <v-icon>keyboard_arrow_down</v-icon>
                </v-btn>
            </div>

            <!-- Step Size Control -->
            <v-row dense class="mb-2">
                <v-col cols="6">
                    <v-text-field
                        v-model.number="moveStep"
                        label="Step"
                        suffix="px"
                        type="number"
                        min="1"
                        max="10"
                        dense
                        outlined
                        hide-details
                    ></v-text-field>
                </v-col>
                <v-col cols="6" class="d-flex align-center justify-center">
                    <v-btn
                        x-small
                        outlined
                        @click="undoLastMove"
                        :disabled="moveHistory.length === 0"
                        color="warning"
                    >
                        <v-icon x-small left>undo</v-icon>
                        Undo
                    </v-btn>
                </v-col>
            </v-row>

            <!-- Movement Status -->
            <v-alert dense outlined color="info" class="mb-0 pa-2">
                <div class="text-caption">
                    <strong>Moved:</strong> X: {{ totalMoveX }}px, Y: {{ totalMoveY }}px
                    <br>
                    <strong>Tip:</strong> Use arrow keys or drag this panel
                </div>
            </v-alert>
        </v-card-text>
    </v-card>

    <UtilityLib ref="utilityLib"></UtilityLib>
</div>`,
    watch: {},
    props: [],
    components: {UtilityLib},
    created() {
        this.interval = setInterval(() => {
            if (map != null) {
                clearInterval(this.interval);
                L.DomEvent.addListener(map, 'pm:lasoostart', this.startLasso, this);
            }
        }, 500);


    },
    computed: {
        precisionPanelStyle() {
            return {
                position: 'fixed',
                top: this.precisionPanelPosition.y + 'px',
                left: this.precisionPanelPosition.x + 'px',
                zIndex: 2000,
                width: '280px',
                cursor: this.isDragging ? 'grabbing' : 'grab',
                userSelect: 'none'
            };
        }
    },
    mounted() {

    },

    data: function () {
        return {
            lasooDialog: false,
            myData: {
                layers: [],
                sameGroup: false,
                groupName: "",
                presets: [],
                groupType: "",
            },
            // Precision movement data
            precisionMoveActive: false,
            moveStep: 1,
            totalMoveX: 0,
            totalMoveY: 0,
            moveHistory: [],
            originalPositions: new Map(),
            keyboardListenerActive: false,
            // Floating panel positioning
            precisionPanelPosition: { x: 20, y: 100 },
            isDragging: false,
        };
    },
    methods: {
        startLasso(e) {
            L.DomEvent.stopPropagation(e);

            this.lassoActive = true;
            globalVueMapInstance.stopEditing();
            globalVueMapInstance.removeItemPopup();


            //this.lasso = map.selectAreaFeature.enable();
            L.DomEvent.addListener(map, 'pm:lasoocreate', this.lassoChange, this);
        },
        getSelectedFeatures(e) {
            const temp = e.layer;
            const layersFound = [];
          
            drawnGroups.eachLayer(player => {
              player.eachLayer(vlayer => {
                try {
                  if (turf.booleanContains(temp.toGeoJSON(15), vlayer.toGeoJSON(15))) {
                    layersFound.push(vlayer);
                  }
                } catch (error) {
                  console.log(error);
                }
              });
            });
          
            return layersFound.length === 0 ? null : layersFound;
          },
          processSelectedLayers() {
            let sameGroup = true;
            const k = [...new Set(this.lassoFeatures)];
            this.myData.layers = [];
          
            k.forEach((item, i) => {
              if (item.options?.type === "lassoselect") {
                k.splice(i, 1);
              }
            });
          
            const group = drawnGroups.searchLayerParent(k[0]._leaflet_id).metadata.shortname;
            const grouptype = this.groupType(drawnGroups.searchLayerParent(k[0]._leaflet_id));
          
            for (const item of k) {
              const temp = drawnGroups.searchLayerParent(item._leaflet_id);
              const thisgrouptype = this.groupType(temp);
          
              if (thisgrouptype !== grouptype) {
                sameGroup = false;
              }
          
              const layer = {
                id: item._leaflet_id,
                groupType: thisgrouptype,
                preset: temp.metadata.shortname,
                groupName: globalMapManager.getPreset(temp.metadata.shortname).name,
                layerName:
                  thisgrouptype !== "Photo"
                    ? item.feature.properties.name
                    : `Photo${item.feature.properties.number}`,
              };
          
              this.myData.layers.push(layer);
            }
          
            this.myData.groupType = grouptype;
            this.myData.sameGroup = sameGroup;
            this.myData.groupName = group;
            this.myData.presets = [];
          
            if (sameGroup) {
              const presets = globalMapManager.getPresets();
          
              if (globalMapManager.getPreset(group).photogroup) {
                for (const property in presets) {
                  if (
                    presets[property].photogroup &&
                    property !== "photos" &&
                    Object.keys(presets[property].leafletlayer._layers).length > 0
                  ) {
                    this.myData.presets.push({
                      value: presets[property].shortname,
                      text: presets[property].name,
                    });
                  }
                }
              } else if (globalMapManager.getPreset(group).markergroup) {
                for (const property in presets) {
                  if (
                    presets[property].markergroup &&
                    property !== "markers" &&
                    Object.keys(presets[property].leafletlayer._layers).length > 0
                  ) {
                    this.myData.presets.push({
                      value: presets[property].shortname,
                      text: presets[property].name,
                    });
                  }
                }
              } else if (globalMapManager.getPreset(group).vectorgroup) {
                for (const property in presets) {
                  if (
                    presets[property].vectorgroup &&
                    property !== "none" &&
                    property !== "hole"
                  ) {
                    this.myData.presets.push({
                      value: presets[property].shortname,
                      text: presets[property].name,
                    });
                  }
                }
              }
            }
          },
       
        groupType(group){
            if (group.metadata.textgroup)
                return "Text";
            if (group.metadata.vectorgroup)
                return "Vector";
            if (group.metadata.photogroup)
                return "Photo";
            if (group.metadata.markergroup)
                return "Marker";
        },
        lassoChange(e) {
            this.lassoFeatures = this.getSelectedFeatures(e);

            map.pm.Draw.disable();

            map.removeLayer(e.layer)
            if (this.lassoFeatures != null) {
                this.processSelectedLayers();
                this.lasooDialog = true;
            }
        },
        changeGroup(e) {
            var grp = e;
            for (var i = 0; i < this.myData.layers.length; i++) {
                var groupID = drawnGroups.searchLayerParent(this.myData.layers[i].id)
                    .metadata
                    .shortname;
                let layer = drawnGroups.searchLayer(this.myData.layers[i].id)
              
                globalMapManager.removeLayerPreset(groupID, layer)
                globalMapManager.addLayerPreset(grp, layer)

                layer.setStyle(
                    globalMapManager.getPreset(grp).leafletlayer.metadata
                );

                layer.feature.properties.preset = grp;
            }

            this.lasooDialog = false;

        },
        deleteAllFeatures() {
            this.$refs.utilityLib.open("Confirm", "Are you sure you want to delete all selected features?", "Yes", "No", true).then(function (result) {
                if (result) {


                    for (var i = 0; i < this.myData.layers.length; i++) {
                        var groupID = drawnGroups.searchLayerParent(this.myData.layers[i].id)
                            .metadata
                            .shortname;

                        globalMapManager.removeLayerPreset(groupID, this.myData.layers[i].id)


                    }

                    this.lasooDialog = false;

                }
            }.bind(this))
        },

        // Simplified Precision Movement Methods
        startPrecisionMove() {
            this.precisionMoveActive = true;
            this.totalMoveX = 0;
            this.totalMoveY = 0;
            this.moveHistory = [];
            this.originalPositions.clear();

            // Store original positions for all selected layers
            this.lassoFeatures.forEach(layer => {
                if (layer instanceof L.Marker) {
                    this.originalPositions.set(layer._leaflet_id, {
                        latlng: layer.getLatLng(),
                        type: 'marker'
                    });
                } else if (layer.getLatLngs) {
                    this.originalPositions.set(layer._leaflet_id, {
                        latlngs: JSON.parse(JSON.stringify(layer.getLatLngs())),
                        type: 'shape'
                    });
                }
            });

            this.setupKeyboardListeners();

            // Position the floating panel near the dialog but not overlapping
            this.positionPrecisionPanel();
        },

        exitPrecisionMove() {
            this.precisionMoveActive = false;
            this.removeKeyboardListeners();
        },

        closeLassoDialog() {
            this.exitPrecisionMove();
            this.lasooDialog = false;
        },

        positionPrecisionPanel() {
            // Position panel to the right of the dialog, or left if not enough space
            const dialogWidth = 400;
            const panelWidth = 280;
            const screenWidth = window.innerWidth;
            const margin = 20;

            if (screenWidth > dialogWidth + panelWidth + margin * 3) {
                // Position to the right of dialog
                this.precisionPanelPosition.x = (screenWidth - dialogWidth) / 2 + dialogWidth + margin;
            } else {
                // Position to the left of dialog
                this.precisionPanelPosition.x = (screenWidth - dialogWidth) / 2 - panelWidth - margin;
            }

            this.precisionPanelPosition.y = 100;
        },

        // Drag functionality for floating panel
        startDrag(event) {
            this.isDragging = true;
            this.dragOffset = {
                x: event.clientX - this.precisionPanelPosition.x,
                y: event.clientY - this.precisionPanelPosition.y
            };
            document.addEventListener('mousemove', this.drag);
            document.addEventListener('mouseup', this.stopDrag);
        },

        drag(event) {
            if (!this.isDragging) return;

            this.precisionPanelPosition.x = event.clientX - this.dragOffset.x;
            this.precisionPanelPosition.y = event.clientY - this.dragOffset.y;

            // Keep panel within screen bounds
            const panelWidth = 280;
            const panelHeight = 200;

            this.precisionPanelPosition.x = Math.max(0, Math.min(window.innerWidth - panelWidth, this.precisionPanelPosition.x));
            this.precisionPanelPosition.y = Math.max(0, Math.min(window.innerHeight - panelHeight, this.precisionPanelPosition.y));
        },

        stopDrag() {
            this.isDragging = false;
            document.removeEventListener('mousemove', this.drag);
            document.removeEventListener('mouseup', this.stopDrag);
        },

        setupKeyboardListeners() {
            if (this.keyboardListenerActive) return;

            this.keyboardListenerActive = true;
            document.addEventListener('keydown', this.handleKeyDown);
        },

        removeKeyboardListeners() {
            if (!this.keyboardListenerActive) return;

            this.keyboardListenerActive = false;
            document.removeEventListener('keydown', this.handleKeyDown);
        },

        handleKeyDown(event) {
            if (!this.precisionMoveActive) return;

            // Prevent default behavior for arrow keys
            if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Escape'].includes(event.key)) {
                event.preventDefault();
            }

            switch (event.key) {
                case 'ArrowUp':
                    this.moveShapes(0, -this.moveStep);
                    break;
                case 'ArrowDown':
                    this.moveShapes(0, this.moveStep);
                    break;
                case 'ArrowLeft':
                    this.moveShapes(-this.moveStep, 0);
                    break;
                case 'ArrowRight':
                    this.moveShapes(this.moveStep, 0);
                    break;
                case 'Escape':
                    this.exitPrecisionMove();
                    break;
            }
        },

        moveShapes(deltaX, deltaY) {
            if (!this.precisionMoveActive || (!deltaX && !deltaY)) return;

            // Store move in history for undo
            const moveRecord = {
                deltaX,
                deltaY,
                timestamp: Date.now()
            };
            this.moveHistory.push(moveRecord);

            // Update total movement
            this.totalMoveX += deltaX;
            this.totalMoveY += deltaY;

            // Convert pixel offset to lat/lng offset
            const pixelOffset = this.pixelsToLatLng(deltaX, deltaY);

            // Move each selected layer
            this.lassoFeatures.forEach(layer => {
                this.moveLayer(layer, pixelOffset.lat, pixelOffset.lng);
            });

            // Update layer properties and trigger save
            this.lassoFeatures.forEach(layer => {
                globalVueMapInstance.updateLayerProps(layer);
                const groupId = drawnGroups.searchLayerParent(layer._leaflet_id);
                if (groupId && groupId.metadata) {
                    globalMapManager.updateLayerInPanel(groupId.metadata.shortname, layer);
                }
            });
        },

        moveLayer(layer, deltaLat, deltaLng) {
            if (layer instanceof L.Marker) {
                const currentPos = layer.getLatLng();
                layer.setLatLng([currentPos.lat + deltaLat, currentPos.lng + deltaLng]);
            } else if (layer.getLatLngs) {
                const currentLatLngs = layer.getLatLngs();
                const newLatLngs = this.offsetLatLngs(currentLatLngs, deltaLat, deltaLng);
                layer.setLatLngs(newLatLngs);
            }
        },

        offsetLatLngs(latlngs, deltaLat, deltaLng) {
            if (Array.isArray(latlngs[0])) {
                // Multi-dimensional array (polygon with holes)
                return latlngs.map(ring => this.offsetLatLngs(ring, deltaLat, deltaLng));
            } else {
                // Simple array of LatLng objects
                return latlngs.map(latlng => ({
                    lat: latlng.lat + deltaLat,
                    lng: latlng.lng + deltaLng
                }));
            }
        },

        pixelsToLatLng(deltaX, deltaY) {
            // Get map bounds and container size to calculate pixel to lat/lng ratio
            const mapBounds = map.getBounds();
            const mapSize = map.getSize();

            const latPerPixel = (mapBounds.getNorth() - mapBounds.getSouth()) / mapSize.y;
            const lngPerPixel = (mapBounds.getEast() - mapBounds.getWest()) / mapSize.x;

            return {
                lat: -deltaY * latPerPixel, // Negative because screen Y increases downward
                lng: deltaX * lngPerPixel
            };
        },

        undoLastMove() {
            if (this.moveHistory.length === 0) return;

            const lastMove = this.moveHistory.pop();

            // Reverse the last move
            this.totalMoveX -= lastMove.deltaX;
            this.totalMoveY -= lastMove.deltaY;

            // Convert pixel offset to lat/lng offset (reverse direction)
            const pixelOffset = this.pixelsToLatLng(-lastMove.deltaX, -lastMove.deltaY);

            // Move each selected layer back
            this.lassoFeatures.forEach(layer => {
                this.moveLayer(layer, pixelOffset.lat, pixelOffset.lng);
            });

            // Update layer properties
            this.lassoFeatures.forEach(layer => {
                globalVueMapInstance.updateLayerProps(layer);
                const groupId = drawnGroups.searchLayerParent(layer._leaflet_id);
                if (groupId && groupId.metadata) {
                    globalMapManager.updateLayerInPanel(groupId.metadata.shortname, layer);
                }
            });
        },


    },

    beforeDestroy() {
        this.removeKeyboardListeners();
    }
}

// Add component styles
const style = document.createElement('style');
style.textContent = `
.precision-move-panel {
    box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
    border-radius: 8px !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.precision-move-panel .v-card__title {
    border-radius: 8px 8px 0 0 !important;
    cursor: grab;
}

.precision-move-panel .v-card__title:active {
    cursor: grabbing;
}

.move-btn {
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

.move-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
}

.move-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0,0,0,0.2) !important;
}

.precision-move-panel .v-alert {
    border-radius: 6px !important;
}
`;

if (!document.head.querySelector('#precision-move-styles')) {
    style.id = 'precision-move-styles';
    document.head.appendChild(style);
}

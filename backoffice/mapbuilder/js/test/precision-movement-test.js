/**
 * Precision Movement Feature Test Suite
 * 
 * This test script validates the precision movement functionality
 * for the lasso tool in the mapbuilder application.
 * 
 * To run these tests:
 * 1. Open the mapbuilder application in a browser
 * 2. Open browser developer console
 * 3. Copy and paste this script into the console
 * 4. Run: runPrecisionMovementTests()
 */

function runPrecisionMovementTests() {
    console.log('🧪 Starting Precision Movement Tests...');
    
    const tests = [
        testLassoControlExists,
        testPrecisionMoveUIElements,
        testKeyboardEventHandlers,
        testMovementCalculations,
        testUndoFunctionality,
        testPreviewFunctionality
    ];
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
        try {
            const result = test();
            if (result) {
                console.log(`✅ ${test.name}: PASSED`);
                passed++;
            } else {
                console.log(`❌ ${test.name}: FAILED`);
                failed++;
            }
        } catch (error) {
            console.log(`❌ ${test.name}: ERROR - ${error.message}`);
            failed++;
        }
    });
    
    console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
    return { passed, failed };
}

function testLassoControlExists() {
    // Check if the lasso control component exists
    const lassoControl = document.querySelector('[data-test="lasso-control"]') || 
                        document.querySelector('.v-dialog') ||
                        (window.globalVueMapInstance && window.globalVueMapInstance.$refs.lassoControl);
    
    return !!lassoControl;
}

function testPrecisionMoveUIElements() {
    // Test if precision movement UI elements are properly defined
    if (!window.globalVueMapInstance || !window.globalVueMapInstance.$refs.lassoControl) {
        console.log('⚠️ Lasso control not found, skipping UI test');
        return true; // Skip test if component not available
    }
    
    const lassoControl = window.globalVueMapInstance.$refs.lassoControl;
    
    // Check if precision movement data properties exist
    const requiredProps = [
        'precisionMoveActive',
        'moveStep',
        'offsetX',
        'offsetY',
        'totalMoveX',
        'totalMoveY',
        'moveHistory',
        'originalPositions'
    ];
    
    return requiredProps.every(prop => lassoControl.hasOwnProperty(prop));
}

function testKeyboardEventHandlers() {
    // Test if keyboard event handlers are properly set up
    if (!window.globalVueMapInstance || !window.globalVueMapInstance.$refs.lassoControl) {
        console.log('⚠️ Lasso control not found, skipping keyboard test');
        return true;
    }
    
    const lassoControl = window.globalVueMapInstance.$refs.lassoControl;
    
    // Check if keyboard handler methods exist
    const requiredMethods = [
        'handleKeyDown',
        'handleKeyUp',
        'setupKeyboardListeners',
        'removeKeyboardListeners'
    ];
    
    return requiredMethods.every(method => typeof lassoControl[method] === 'function');
}

function testMovementCalculations() {
    // Test pixel to lat/lng conversion calculations
    if (!window.map) {
        console.log('⚠️ Map not found, skipping movement calculation test');
        return true;
    }
    
    // Mock lasso control for testing
    const mockLassoControl = {
        pixelsToLatLng(deltaX, deltaY) {
            const mapBounds = map.getBounds();
            const mapSize = map.getSize();
            
            const latPerPixel = (mapBounds.getNorth() - mapBounds.getSouth()) / mapSize.y;
            const lngPerPixel = (mapBounds.getEast() - mapBounds.getWest()) / mapSize.x;
            
            return {
                lat: -deltaY * latPerPixel,
                lng: deltaX * lngPerPixel
            };
        }
    };
    
    // Test conversion with known values
    const result = mockLassoControl.pixelsToLatLng(10, 10);
    
    // Check if result has expected properties and reasonable values
    return result && 
           typeof result.lat === 'number' && 
           typeof result.lng === 'number' &&
           !isNaN(result.lat) && 
           !isNaN(result.lng);
}

function testUndoFunctionality() {
    // Test undo functionality logic
    if (!window.globalVueMapInstance || !window.globalVueMapInstance.$refs.lassoControl) {
        console.log('⚠️ Lasso control not found, skipping undo test');
        return true;
    }
    
    const lassoControl = window.globalVueMapInstance.$refs.lassoControl;
    
    // Check if undo method exists
    return typeof lassoControl.undoLastMove === 'function';
}

function testPreviewFunctionality() {
    // Test preview functionality
    if (!window.globalVueMapInstance || !window.globalVueMapInstance.$refs.lassoControl) {
        console.log('⚠️ Lasso control not found, skipping preview test');
        return true;
    }
    
    const lassoControl = window.globalVueMapInstance.$refs.lassoControl;
    
    // Check if preview methods exist
    const previewMethods = [
        'previewMove',
        'createPreviewLayers',
        'clearPreviewLayers'
    ];
    
    return previewMethods.every(method => typeof lassoControl[method] === 'function');
}

// Manual test functions for interactive testing
function testPrecisionMovementInteractive() {
    console.log('🎮 Interactive Precision Movement Test');
    console.log('1. Use the lasso tool to select some shapes');
    console.log('2. Open the lasso dialog and switch to "Precision Move" tab');
    console.log('3. Click "Enter Move Mode"');
    console.log('4. Try using arrow keys or input fields to move shapes');
    console.log('5. Test undo functionality');
    console.log('6. Test preview mode');
    
    if (window.globalVueMapInstance && window.globalVueMapInstance.$refs.lassoControl) {
        const lassoControl = window.globalVueMapInstance.$refs.lassoControl;
        console.log('Current precision move state:', {
            active: lassoControl.precisionMoveActive,
            totalMoveX: lassoControl.totalMoveX,
            totalMoveY: lassoControl.totalMoveY,
            historyLength: lassoControl.moveHistory.length
        });
    }
}

function simulateKeyboardInput(key) {
    const event = new KeyboardEvent('keydown', {
        key: key,
        code: `Key${key.toUpperCase()}`,
        keyCode: key.charCodeAt(0),
        which: key.charCodeAt(0),
        bubbles: true
    });
    
    document.dispatchEvent(event);
    console.log(`Simulated keyboard input: ${key}`);
}

// Export functions for console use
window.runPrecisionMovementTests = runPrecisionMovementTests;
window.testPrecisionMovementInteractive = testPrecisionMovementInteractive;
window.simulateKeyboardInput = simulateKeyboardInput;

console.log('🔧 Precision Movement Test Suite Loaded');
console.log('Run: runPrecisionMovementTests() for automated tests');
console.log('Run: testPrecisionMovementInteractive() for manual testing');
